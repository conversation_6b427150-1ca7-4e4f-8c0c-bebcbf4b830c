from rest_framework.exceptions import APIException
from rest_framework import status


class MobileAppNotFoundError(APIException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = "Mobile app not found"
    default_code = "mobile_app_not_found"

    def __init__(self, detail=None, code=None):
        super().__init__(detail or self.default_detail, code)


class UserNotFoundError(APIException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = "User not found"
    default_code = "user_not_found"

    def __init__(self, detail=None, code=None):
        super().__init__(detail or self.default_detail, code)


class InvalidCookieError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Invalid cookie"
    default_code = "invalid_cookie"

    def __init__(self, detail=None, code=None):
        super().__init__(detail or self.default_detail, code)


class ExternalAPIError(APIException):
    status_code = status.HTTP_502_BAD_GATEWAY
    default_detail = "External API error"
    default_code = "external_api_error"

    def __init__(self, detail=None, code=None):
        super().__init__(detail or self.default_detail, code)