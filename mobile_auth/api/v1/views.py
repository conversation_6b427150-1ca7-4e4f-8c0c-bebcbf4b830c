from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from users.api.v1.serializers.shared_serializers import TokenResponseSerializer
from ...exceptions import InvalidCookieError
from ...services.mobile_auth_service import MobileAuthService


class MobileAuthTokenView(APIView):
    """
    Получение токена авторизации через мобильное приложение
    """
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        responses={
            200: TokenResponseSerializer,
            400: openapi.Response('Bad Request'),
            404: openapi.Response('User not found'),
            502: openapi.Response('External API error')
        },
        operation_description="Получение токена авторизации через cookie мобильного приложения"
    )
    def post(self, request):
        app_session = self._get_app_session_cookie(request)

        auth_service = MobileAuthService('kaspi_people')
        token = auth_service.authenticate_user(cookie=app_session)

        return Response(token, status=status.HTTP_200_OK)

    def _get_app_session_cookie(self, request):
        app_session = request.COOKIES.get('appSession')

        if not app_session:
            raise InvalidCookieError('appSession cookie is required')

        if len(app_session.strip()) == 0:
            raise InvalidCookieError('appSession cookie cannot be empty')

        return app_session