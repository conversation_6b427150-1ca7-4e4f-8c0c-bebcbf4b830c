from django.db import models
from django.utils.translation import gettext_lazy as _

from core.models import BaseUUIDModel
from users.models import User


class UserMobileProfile(BaseUUIDModel):
    user = models.ForeignKey(to=User, on_delete=models.CASCADE, related_name='mobile_profiles')
    app_name = models.CharField(max_length=100, help_text="Название мобильного приложения")
    external_user_id = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'mobile_auth_profiles'
        unique_together = ('user', 'app_name', 'external_user_id')
        verbose_name = _('User Mobile Profile')
        verbose_name_plural = _('User Mobile Profiles')

    def __str__(self):
        return f"{self.user.username} - {self.app_name}"


