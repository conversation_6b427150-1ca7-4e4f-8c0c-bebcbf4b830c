# Generated by Django 5.1.2 on 2025-08-15 10:44

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserMobileProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('app_name', models.Char<PERSON>ield(help_text='Название мобильного приложения', max_length=100)),
                ('external_user_id', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mobile_profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Mobile Profile',
                'verbose_name_plural': 'User Mobile Profiles',
                'db_table': 'mobile_auth_profiles',
                'unique_together': {('user', 'app_name', 'external_user_id')},
            },
        ),
    ]
