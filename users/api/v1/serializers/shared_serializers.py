from rest_framework import serializers

from users.api.v1.serializers.permission_serializers import (
    PermissionRecursiveSerializer,
)
from users.api.v1.serializers.role_serializers import RoleManagementSerializer
from users.api.v1.serializers.user_serializers import (
    CountrySerializer,
    UserCompanySerializer,
    UserStatusSerializer,
)
from users.models import ContentType, User


class ContentTypeSerializer(serializers.ModelSerializer):
    permissions = PermissionRecursiveSerializer(many=True)

    class Meta:
        model = ContentType
        fields = ("id", "name", "code", "permissions")


class UserManagementSerializer(serializers.ModelSerializer):
    statuses = UserStatusSerializer(many=True, read_only=True)
    roles = RoleManagementSerializer(many=True, read_only=True)
    country = CountrySerializer(read_only=True)
    companies = UserCompanySerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = (
            "id",
            "username",
            "first_name",
            "last_name",
            "photo",
            "email",
            "iin",
            "companies",
            "statuses",
            "roles",
            "groups_count",
            "date_joined",
            "last_login",
            "phone",
            "country",
            "birth_date",
            "groups_count",
            "date_joined",
            "last_login",
            "phone",
            "country",
            "birth_date",
        )


class TokenResponseSerializer(serializers.Serializer):
    access = serializers.CharField(help_text='Refresh Token')
    refresh = serializers.CharField(help_text='Access Token')
