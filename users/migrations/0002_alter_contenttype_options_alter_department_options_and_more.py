# Generated by Django 5.1.2 on 2025-08-15 10:44

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='contenttype',
            options={'ordering': ['id'], 'verbose_name': 'content type', 'verbose_name_plural': 'content types'},
        ),
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['id'], 'verbose_name': 'department', 'verbose_name_plural': 'departments'},
        ),
        migrations.AlterModelOptions(
            name='permission',
            options={'ordering': ['id'], 'verbose_name': 'permission', 'verbose_name_plural': 'permissions'},
        ),
        migrations.AlterModelOptions(
            name='role',
            options={'ordering': ['id'], 'verbose_name': 'role', 'verbose_name_plural': 'roles'},
        ),
        migrations.AlterModelOptions(
            name='status',
            options={'ordering': ['id'], 'verbose_name': 'status', 'verbose_name_plural': 'statuses'},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ['id'], 'verbose_name': 'user', 'verbose_name_plural': 'users'},
        ),
        migrations.AlterModelOptions(
            name='userschedule',
            options={'ordering': ['id'], 'verbose_name': 'user blocking schedule', 'verbose_name_plural': 'user blocking schedules'},
        ),
        migrations.AlterModelOptions(
            name='userstatus',
            options={'ordering': ['id'], 'verbose_name': 'user status', 'verbose_name_plural': 'user statuses'},
        ),
    ]
