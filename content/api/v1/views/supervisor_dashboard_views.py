from datetime import datetime
from uuid import UUID

from django.db.models import (
    Count,
    ExpressionWrapper,
    F,
    <PERSON>loat<PERSON>ield,
    Prefetch,
    Q,
    QuerySet,
)
from django.db.models.functions import Greatest
from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics
from rest_framework.filters import SearchFilter
from rest_framework.generics import (
    ListAPIView,
    RetrieveAPIView,
    RetrieveUpdateAPIView,
)
from rest_framework.permissions import (
    IsAuthenticated,
)
from rest_framework.response import Response

from content.api.v1.serializers.course_serializers import CoursesEnvelopeSerializer
from content.api.v1.serializers.supervisor_dashboard_serializers import (
    GroupedSupervisedDepartmentListSerializer,
    StatisticsCourseExpiredSerializer,
    StatisticsCourseFinishedSerializer,
    StatisticsCourseProgressSerializer,
)
from content.api.v1.utils import (
    create_recursive_serializer,
    get_all_descendants_and_annotate,
    get_recursive_supervised_user_ids,
    user_is_supervisor_in_parent_department,
)
from content.models import Enrollment
from lms.utils import CustomPagination, get_paginated_serializer
from users.models import (
    Department,
    DepartmentSupervisor,
    User,
    UserCompany,
    UserSupervisorSettings,
)

from ..serializers import (
    DashboardMemberSerializer,
    SupervisedUserSerializer,
    UserSupervisorSettingsSerializer,
)
from . import MyCoursesAPIView


class SupervisorDashboardAPIView(ListAPIView):
    serializer_class = SupervisedUserSerializer
    pagination_class = CustomPagination
    filter_backends = [SearchFilter]
    permission_classes = [IsAuthenticated]
    search_fields = ["full_name", "email", "username"]

    def get_queryset(self) -> QuerySet[User]:
        user_id: UUID = self.request.user.id
        settings, _ = UserSupervisorSettings.objects.get_or_create(user_id=user_id)

        enroll_qs: QuerySet[Enrollment] = Enrollment.objects.select_related(
            "resource"
        ).filter(~Q(status=Enrollment.FINISHED))

        companies_qs: QuerySet[UserCompany] = UserCompany.objects.select_related(
            'company',
            'department__parent',
            'department',
            'position'
        )

        department_id: UUID = self.request.query_params.get("department_id")
        if department_id:
            if user_is_supervisor_in_parent_department(user_id, department_id):
                department: Department = get_object_or_404(Department, pk=department_id)
                dept_ids = department.get_descendant_ids_cte()
                qs: QuerySet[User] = User.objects.filter(companies__department__id__in=dept_ids)
            else:
                return User.objects.none()
        else:
            supervised_users_ids: list[UUID] = get_recursive_supervised_user_ids(user_id)
            qs: QuerySet[User] = User.objects.filter(id__in=supervised_users_ids)

        qs: QuerySet[User] = (
            qs
            .annotate(
                total_enrollments=Count('enrollments', distinct=True),
                finished_enrollments=Count(
                    'enrollments',
                    filter=Q(enrollments__status=Enrollment.FINISHED),
                    distinct=True
                ),
                not_finished_manually_count=Count(
                    'enrollments',
                    filter=Q(enrollments__status=Enrollment.NOT_FINISHED_MANUALLY),
                    distinct=True
                ),
                expired_enrollments_count=Count(
                    'enrollments',
                    filter=Q(enrollments__due_date_settings__date__lte=datetime.now()),
                    distinct=True
                ),
            )
            .annotate(
                progress_percent=ExpressionWrapper(
                    100.0 * F('finished_enrollments') / Greatest(F('total_enrollments'), 1),
                    output_field=FloatField()
                )
            )
            .prefetch_related(
                Prefetch('enrollments', queryset=enroll_qs, to_attr='not_finished_enrollments'),
                Prefetch('companies', queryset=companies_qs, to_attr='user_companies'),
            )
            .distinct()
        )

        filter_type = self.request.query_params.get('training_status')

        if filter_type == 'low':
            qs = qs.order_by('progress_percent')
        elif filter_type == 'high':
            qs = qs.order_by('-progress_percent')

        finished: str = self.request.query_params.get("finished")
        if finished == "true":
            qs = qs.filter(enrollments__status=Enrollment.FINISHED)

        not_finished: str = self.request.query_params.get("not_finished")
        if not_finished == "true":
            qs = qs.filter(
                Q(enrollments__status=Enrollment.NOT_STARTED)
                | Q(enrollments__status=Enrollment.IN_PROGRESS)
            )

        not_logged_in: str = self.request.query_params.get("not_logged_in")
        if not_logged_in == "true":
            qs = qs.filter(last_login__isnull=True)

        expired_courses: str = self.request.query_params.get("expired_courses")
        if expired_courses == "true":
            qs = qs.filter(enrollments__due_date_settings__date__lt=timezone.now())

        expired_certificates: str = self.request.query_params.get("expired_certificates")
        if expired_certificates == "true":
            qs = qs.filter(enrollments__certification__expired_date__lt=timezone.now())

        return qs

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "training_status",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Статус обученности (низкая/высокая)",
            ),
            openapi.Parameter(
                "department_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="UUID департамента",
            ),
            openapi.Parameter(
                "finished",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Завершенные курсы",
            ),
            openapi.Parameter(
                "not_finished",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Незавершенные курсы",
            ),
            openapi.Parameter(
                "not_logged_in",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Не заходил на портал",
            ),
            openapi.Parameter(
                "expired_courses",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Просроченные курсы",
            ),
            openapi.Parameter(
                "expired_certificates",
                openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description="Просроченные сертификаты",
            ),
        ],
        responses={200: get_paginated_serializer(serializer_class)},
    )
    def get(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class GroupedSupervisedDepartmentListView(generics.ListAPIView):
    queryset = Department.objects.all()
    serializer_class = GroupedSupervisedDepartmentListSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination
    filter_backends = [SearchFilter]
    search_fields = [
        "name",
    ]

    def get_queryset(self):
        user: User = self.request.user
        department_id: UUID = self.request.query_params.get("department_id")

        if department_id:
            if user_is_supervisor_in_parent_department(user.id, department_id):
                base_departments: QuerySet[Department] = Department.objects.filter(
                    parent_id=department_id
                )
            else:
                return Department.objects.none()
        else:
            base_departments: QuerySet[Department] = Department.objects.filter(
                supervisors_in_department__supervisor=user,
            )

        result_departments_list: list[Department] = list(
            base_departments.select_related("company", "parent").prefetch_related(
                "children",
                "supervisors",
            )
        )
        if not result_departments_list:
            return Department.objects.none()

        dept_ids: list[UUID] = [department.id for department in result_departments_list]
        descendants_and_annotated_data: dict[UUID, dict] = (
            get_all_descendants_and_annotate(dept_ids)
        )

        for department in result_departments_list:
            annotated_department_attrs: dict = descendants_and_annotated_data.get(
                department.id
            )
            for key, value in annotated_department_attrs.items():
                setattr(department, key, value)

        return result_departments_list

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "department_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Department UUID",
            ),
            openapi.Parameter(
                "search",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Search Department by name"
            ),
        ],
        responses={200: get_paginated_serializer(create_recursive_serializer(serializer_class))},
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

class UserSupervisorSettingsViewSet(RetrieveUpdateAPIView):
    serializer_class = UserSupervisorSettingsSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        obj, created = UserSupervisorSettings.objects.get_or_create(user=self.request.user)
        return obj

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def patch(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class SupervisorDashboardMemberAPIView(RetrieveAPIView):
    """
    Получить детальную карточку подчинённого (с аффилиацией, позицией, супервизорами)
    """
    permission_classes = [IsAuthenticated]
    serializer_class = DashboardMemberSerializer

    def get_queryset(self):
        companies_qs = UserCompany.objects.select_related(
            'company',
            'department__parent',
            'department',
            'position'
        )

        return User.objects.prefetch_related(
            Prefetch(
                'companies',
                queryset=companies_qs,
                to_attr='user_companies'
            ),
            'supervisors',
        ).select_related('country')

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'user_id',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='User UUID'
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        user = get_object_or_404(self.get_queryset(), pk=request.query_params.get('user_id'))
        serializer = self.serializer_class(user)

        return Response(serializer.data)


class SupervisorDashboardEnrollmentsAPIView(MyCoursesAPIView):
    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return Enrollment.objects.none()
        return (
            Enrollment.objects.filter(user=self.request.query_params.get("user_id"))
            .select_related(
                "resource",
                "resource__detail",
                "due_date_settings",
                "resource__detail__instructor",
                "resource__owner",
            )
            .prefetch_related("resource__detail__tags")
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'user_id',
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='User UUID'
            )
        ],
        responses={200: CoursesEnvelopeSerializer})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class StatisticsCourseProgressView(generics.GenericAPIView):
    serializer_class = StatisticsCourseProgressSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        user: User = self.request.user
        qs: QuerySet[Enrollment] = Enrollment.objects.filter(
            Q(user__supervisors_in_user__supervisor=user) |
            Q(
                user__companies__department__supervisors_in_department__supervisor_id=user
            )
        )

        department_id = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(user__companies__department_id=department_id)

        return (
            qs
            .distinct()
            .aggregate(
                total_count=Count(
                    "id",
                    distinct=True,
                ),
                finished_count=Count(
                    "status",
                    filter=Q(status=Enrollment.FINISHED),
                ),
                in_progress_count=Count(
                    "status",
                    filter=Q(status=Enrollment.IN_PROGRESS),
                ),
                finished_manually_count=Count(
                    "status",
                    filter=Q(status=Enrollment.FINISHED_MANUALLY),
                ),
                not_started_count=Count(
                    "status",
                    filter=Q(status=Enrollment.NOT_STARTED),
                ),
            )
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="department_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Department UUID",
            )
        ],
        responses={200: serializer_class},
    )
    def get(self, request, *args, **kwargs):
        qs: dict = self.filter_queryset(self.get_queryset())

        total_count = qs.get("total_count")
        qs["finished_percent"] = self.calculate_percent(qs.get("finished_count"), total_count)
        qs["in_progress_percent"] = self.calculate_percent(qs.get("in_progress_count"), total_count)
        qs["finished_manually_percent"] = self.calculate_percent(qs.get("finished_manually_count"), total_count)
        qs["not_started_percent"] = self.calculate_percent(qs.get("not_started_count"), total_count)

        serializer: StatisticsCourseProgressSerializer = self.get_serializer(qs)
        return Response(serializer.data)

    @staticmethod
    def calculate_percent(value: int, total_count: int) -> float:
        # Чтобы не делить на 0
        if total_count == 0:
            return 0
        return round((value / total_count) * 100, 2)


class StatisticsCourseFinishedView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StatisticsCourseFinishedSerializer
    pagination_class = None

    def get_queryset(self):
        user: User = self.request.user
        qs: QuerySet[Enrollment] = Enrollment.objects.filter(
            Q(user__supervisors_in_user__supervisor=user) |
            Q(
                user__companies__department__supervisors_in_department__supervisor_id=user
            )
        )

        department_id = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(user__companies__department_id=department_id)

        return qs.aggregate(
            total_count=Count(
                "id",
                distinct=True,
            ),
            finished_count=Count(
                "status",
                filter=Q(status=Enrollment.FINISHED)
                | Q(status=Enrollment.FINISHED_MANUALLY),
            ),
            not_finished_count=Count(
                "status",
                filter=Q(status=Enrollment.IN_PROGRESS)
                | Q(status=Enrollment.NOT_STARTED),
            ),
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="department_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Department UUID",
            )
        ],
        responses={200: serializer_class},
    )
    def get(self, request, *args, **kwargs):
        qs: dict = self.filter_queryset(self.get_queryset())
        serializer: StatisticsCourseFinishedSerializer = self.get_serializer(qs)
        return Response(serializer.data)


class StatisticsCourseExpiredView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StatisticsCourseExpiredSerializer
    pagination_class = None

    def get_queryset(self):
        user: User = self.request.user
        qs: QuerySet[Enrollment] = Enrollment.objects.filter(
            Q(user__supervisors_in_user__supervisor=user)
            | Q(
                user__companies__department__supervisors_in_department__supervisor_id=user
            )
        )

        department_id = self.request.query_params.get("department_id")
        if department_id:
            qs = qs.filter(user__companies__department_id=department_id)

        return qs.aggregate(
            not_logged_in=Count("user", filter=Q(user__last_login__isnull=True)),
            expired_courses=Count(
                "due_date_settings__date",
                filter=Q(due_date_settings__date__lt=timezone.now()),
            ),
            expired_certificates=Count(
                "certification",
                filter=Q(certification__expired_date__lt=timezone.now()),
            ),
        )

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                name="department_id",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Department UUID",
            )
        ],
        responses={200: serializer_class},
    )
    def get(self, request, *args, **kwargs):
        qs: dict = self.filter_queryset(self.get_queryset())
        serializer: StatisticsCourseExpiredSerializer = self.get_serializer(qs)
        return Response(serializer.data)
