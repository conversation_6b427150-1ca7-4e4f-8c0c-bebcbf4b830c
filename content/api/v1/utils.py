from datetime import timedelta
from uuid import UUID

from dateutil.relativedelta import relativedelta
from django.db import connection
from django.forms import model_to_dict
from rest_framework import serializers

from content.models import DueDateSettings, Enrollment, Preference, Settings
from users.models import Department, DepartmentSupervisor, UserCompany


def coerce_types(data):
    """
    Функция для проверки типов данных при валидации json схемы
    :param data: dict
    :return: dict: clean
    """
    if isinstance(data, dict):
        return {k: coerce_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [coerce_types(v) for v in data]
    elif isinstance(data, str) and data.isdigit():
        return int(data)
    return data


def get_settings_for_resource(resource_id: int, preference_code: str) -> dict:
        """
        Получение настроек для определенного ресурса
        :param resource_id: id ресурса
        :param resource_type: LONGREAD, CHAPTER, STAGE и т.д.
        :return:
        """
        settings_qs = Settings.objects.filter(
            resource_id=resource_id,
            preference__code=preference_code,
        ).first()

        if not settings_qs:
            return {}

        return model_to_dict(settings_qs)


def due_date(type, period_unit, period, enrollment, date):
    """
    Рассчитывает финальную дату дедлайна в зависимости от типа.
    """

    if type == DueDateSettings.TYPE_DUE_DATE:
        return date

    elif type == DueDateSettings.TYPE_DUE_PERIOD:
        if period is None or not period_unit:
            return None

        if period_unit == DueDateSettings.PERIOD_DAY:
            return enrollment.access_date + timedelta(days=period)
        elif period_unit == DueDateSettings.PERIOD_WEEK:
            return enrollment.access_date + timedelta(weeks=period)
        elif period_unit == DueDateSettings.PERIOD_MONTH:
            return enrollment.access_date + relativedelta(months=period)
        elif period_unit == DueDateSettings.PERIOD_YEAR:
            return enrollment.access_date + relativedelta(years=period)

    elif type == DueDateSettings.TYPE_DEFAULT:
        resource_type = enrollment.resource.type
        pref_code = f"{resource_type}_ACCESS_MANAGEMENT_DEFAULT_ASSIGNMENT_PARAMETERS"
        preference = Preference.objects.filter(
            section=Preference.ACCESS_CONTROL,
            code=pref_code
        ).first()
        if not preference:
            return None

        settings = Settings.objects.filter(
            resource_id=enrollment.resource_id,
            preference_id=preference.id
        ).first()

        if not settings:
            return None

        payload = settings.payload or {}
        term = payload.get("term")
        if term == DueDateSettings.TYPE_DUE_PERIOD:
            units = payload.get("units")
            value = payload.get("value")

            if not units or value is None:
                return None

            if units == DueDateSettings.PERIOD_DAY:
                return enrollment.access_date + timedelta(days=value)
            elif units == DueDateSettings.PERIOD_WEEK:
                return enrollment.access_date + timedelta(weeks=value)
            elif units == DueDateSettings.PERIOD_MONTH:
                return enrollment.access_date + relativedelta(months=value)
            elif units == DueDateSettings   .PERIOD_YEAR:
                return enrollment.access_date + relativedelta(years=value)

        elif term == DueDateSettings.TYPE_UNLIMITED:
            return None

    return None  # для UNLIMITED


def create_recursive_serializer(base_serializer_class):
    class_name = f"{base_serializer_class.__name__}Recursive"
    """
    Данная функция нужна только для корректного отображения документации для рекурсивных сериализаторов

    Под рекурсивными сериализаторами мы понимаем объект Resource.children = Resource
    """
    class DynamicRecursiveSerializer(base_serializer_class):
        children = serializers.ListSerializer(child=base_serializer_class())

        class Meta(base_serializer_class.Meta):
            ref_name = class_name

    return DynamicRecursiveSerializer


def get_recursive_supervised_user_ids(supervisor_id: UUID) -> list[UUID]:
    """
    Получить всех прямых и вложенных подчинённых для заданного руководителя.
    """
    query = """
            WITH RECURSIVE supervised_users AS (
                SELECT user_id
                FROM users_user_supervisors
                WHERE supervisor_id = %s

                UNION ALL

                SELECT s.user_id
                FROM users_user_supervisors s
                INNER JOIN supervised_users su ON su.user_id = s.supervisor_id
            )
            SELECT user_id FROM supervised_users
        """
    with connection.cursor() as cursor:
        cursor.execute(query, [supervisor_id])
        return [row[0] for row in cursor.fetchall()]


def user_is_supervisor_in_parent_department(user_id: UUID, department_id: UUID) -> bool:
    department_table = Department._meta.db_table
    department_supervisor_table = DepartmentSupervisor._meta.db_table
    query = f"""
    WITH RECURSIVE parent_tree AS (
        SELECT id, parent_id
        FROM {department_table}
        WHERE id = %(department_id)s

        UNION ALL

        SELECT d.id, d.parent_id
        FROM {department_table} d
        INNER JOIN parent_tree pt ON pt.parent_id = d.id
    )

    SELECT EXISTS (
        SELECT 1
        FROM parent_tree pt
        JOIN {department_supervisor_table} ds
            ON ds.department_id = pt.id
        WHERE ds.supervisor_id = %(user_id)s
    ) AS is_supervisor;
    """

    with connection.cursor() as cursor:
        cursor.execute(
            query, {"department_id": str(department_id), "user_id": str(user_id)}
        )
        result = cursor.fetchone()[0]

    return bool(result)


def get_all_descendants_and_annotate(
    department_ids: list
) -> dict[UUID, dict]:
    """
    return dict: {
        parent_id: [
            descendant_id1,
            descendant_id2,
            ...
        ],
        "finished_enrollments": <int>,
        "not_finished_enrollments": <int>,
        ...
    }
    """
    DESCENDANT_IDS = "descendant_ids"
    USER_COUNT = "user_count"
    TOTAL_ENROLLMENTS = "total_enrollments"
    FINISHED_ENROLLMENTS = "finished_enrollments"
    NOT_FINISHED_ENROLLMENTS = "not_finished_enrollments"
    FINISHED_MANUALLY_ENROLLMENTS = "finished_manually_enrollments"
    NOT_FINISHED_MANUALLY_ENROLLMENTS = "not_finished_manually_enrollments"
    NOT_STARTED_ENROLLMENTS = "not_started_enrollments"
    IN_PROGRESS_ENROLLMENTS = "in_progress_enrollments"
    EXPIRED_ENROLLMENTS = "expired_enrollments"

    if not department_ids:
        return {}

    department_table = Department._meta.db_table
    usercompany_table = UserCompany._meta.db_table
    enrollment_table = Enrollment._meta.db_table
    duedatesettings_table = DueDateSettings._meta.db_table

    placeholders = ",".join(["%s"] * len(department_ids))  # <- против sql инъекций

    sql = f"""
    WITH RECURSIVE department_tree AS (
        SELECT id AS root_id, id AS descendant_id
        FROM {department_table}
        WHERE id IN ({placeholders})
    UNION ALL
        SELECT dt.root_id, d.id
        FROM {department_table} d
        JOIN department_tree dt ON d.parent_id = dt.descendant_id
    )
    SELECT
        dt.root_id,
        ARRAY_AGG(DISTINCT dt.descendant_id)                         AS {DESCENDANT_IDS},
        COUNT(DISTINCT uc.user_id)                                   AS {USER_COUNT},
        COUNT(DISTINCT e.id)                                         AS {TOTAL_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (WHERE e.status = %s)            AS {FINISHED_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (WHERE e.status != %s)           AS {NOT_FINISHED_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (WHERE e.status = %s)            AS {FINISHED_MANUALLY_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (WHERE e.status = %s)            AS {NOT_FINISHED_MANUALLY_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (WHERE e.status = %s)            AS {NOT_STARTED_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (WHERE e.status = %s)            AS {IN_PROGRESS_ENROLLMENTS},
        COUNT(DISTINCT e.id) FILTER (
            WHERE dds.date IS NOT NULL AND dds.date <= CURRENT_DATE
        )                                                            AS {EXPIRED_ENROLLMENTS}
    FROM department_tree dt
    LEFT JOIN {usercompany_table}     uc  ON uc.department_id        = dt.descendant_id
    LEFT JOIN {enrollment_table}      e   ON e.user_id               = uc.user_id
    LEFT JOIN {duedatesettings_table} dds ON dds.enrollment_id       = e.id
    GROUP BY dt.root_id
    ORDER BY dt.root_id;
    """

    params = [
        *department_ids,
        Enrollment.FINISHED,
        Enrollment.FINISHED,
        Enrollment.FINISHED_MANUALLY,
        Enrollment.NOT_FINISHED_MANUALLY,
        Enrollment.NOT_STARTED,
        Enrollment.IN_PROGRESS,
    ]

    with connection.cursor() as cursor:
        cursor.execute(sql, params)
        results = cursor.fetchall()

    # Group results by root department
    mappings = {
        root_id: {
            f"{DESCENDANT_IDS}": descendant_ids,
            f"{USER_COUNT}": user_count,
            f"{TOTAL_ENROLLMENTS}": total_enrollments,
            f"{FINISHED_ENROLLMENTS}": finished_enrollments,
            f"{NOT_FINISHED_ENROLLMENTS}": not_finished_enrollments,
            f"{FINISHED_MANUALLY_ENROLLMENTS}": finished_manually_enrollments,
            f"{NOT_FINISHED_MANUALLY_ENROLLMENTS}": not_finished_manually_enrollments,
            f"{NOT_STARTED_ENROLLMENTS}": not_started_enrollments,
            f"{IN_PROGRESS_ENROLLMENTS}": in_progress_enrollments,
            f"{EXPIRED_ENROLLMENTS}": expired_enrollments,
            "progress_percent": 100.0 * finished_enrollments / max(total_enrollments, 1) if total_enrollments > 0 else 0.0
        }
        for (
            root_id,
            descendant_ids,
            user_count,
            total_enrollments,
            finished_enrollments,
            not_finished_enrollments,
            finished_manually_enrollments,
            not_finished_manually_enrollments,
            not_started_enrollments,
            in_progress_enrollments,
            expired_enrollments,
        ) in results
    }

    return mappings