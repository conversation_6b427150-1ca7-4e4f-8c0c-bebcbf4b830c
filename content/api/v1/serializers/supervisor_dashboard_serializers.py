from rest_framework import serializers

from content.models import Department, Enrollment, Resource
from users.api.v1.serializers import UserEnrollmentSerializer
from users.api.v1.serializers.department_serializers import (
    CompanySerializer,
    DepartmentSerializer,
)
from users.api.v1.serializers.user_serializers import UserSerializer
from users.models import User, UserSupervisorSettings


class ResourceTitleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Resource
        fields = ["name", "type"]


class NotFinishedEnrollmentSerializer(serializers.ModelSerializer):
    resource = ResourceTitleSerializer()

    class Meta:
        model = Enrollment
        fields = ["id", "resource"]


class SupervisedUserSerializer(serializers.ModelSerializer):
    full_name = serializers.CharField()
    total_enrollments = serializers.IntegerField(
        help_text="Общее количество курсов, на которые пользователь записан"
    )
    finished_enrollments = serializers.IntegerField(
        help_text="Количество курсов, которые пользователь завершил"
    )
    not_finished_manually_count = serializers.IntegerField(
        help_text="Количество курсов со статусом NOT_FINISHED_MANUALLY"
    )
    progress_percent = serializers.FloatField(
        help_text="Процент завершённых курсов относительно общего количества"
    )
    not_finished_enrollments = NotFinishedEnrollmentSerializer(
        many=True, help_text="Список курсов со статусом NOT_FINISHED_MANUALLY"
    )
    expired_enrollments_count = serializers.IntegerField(
        help_text="Список просроченных курсов"
    )
    position = serializers.SerializerMethodField(
        help_text="Название позиции пользователя"
    )
    department_path = serializers.SerializerMethodField(
        help_text="Путь департамента пользователя"
    )

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "full_name",
            "department_path",
            "position",
            "total_enrollments",
            "finished_enrollments",
            "not_finished_manually_count",
            "progress_percent",
            "not_finished_enrollments",
            "expired_enrollments_count",
        ]

    def get_position(self, obj):
        aff = getattr(obj, "user_companies", [])
        if aff:
            return aff[0].position.name
        return None

    def get_department_path(self, obj):
        aff = getattr(obj, "user_companies", [])
        if aff:
            dept = aff[0].department
            parts = []
            if dept.parent:
                parts.append(dept.parent.name)
            parts.append(dept.name)
            return " / ".join(parts)
        return None


class GroupedSupervisedDepartmentAbstract(serializers.ModelSerializer):
    user_count = serializers.IntegerField(default=0)
    total_enrollments = serializers.IntegerField(default=0)
    finished_enrollments = serializers.IntegerField(default=0)
    not_finished_enrollments = serializers.IntegerField(default=0)
    finished_manually_enrollments = serializers.IntegerField(default=0)
    not_finished_manually_enrollments = serializers.IntegerField(default=0)
    not_started_enrollments = serializers.IntegerField(default=0)
    in_progress_enrollments = serializers.IntegerField(default=0)
    progress_percent = serializers.FloatField(default=0)

    class Meta:
        fields = (
            "user_count",
            "total_enrollments",
            "finished_enrollments",
            "not_finished_enrollments",
            "finished_manually_enrollments",
            "not_finished_manually_enrollments",
            "not_started_enrollments",
            "in_progress_enrollments",
            "progress_percent",
        )


class GroupedSupervisedDepartmentListSerializer(GroupedSupervisedDepartmentAbstract):
    children = DepartmentSerializer(many=True, read_only=True)
    parent = DepartmentSerializer(read_only=True)
    company = CompanySerializer(read_only=True)
    supervisors = UserSerializer(many=True)

    class Meta:
        model = Department
        fields = (
            "id",
            "name",
            "parent",
            "children",
            "company",
            "supervisors",
        ) + GroupedSupervisedDepartmentAbstract.Meta.fields


class UserSupervisorSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserSupervisorSettings
        fields = [
            "id",
            "user",
            "expiring_certificates",
            "expired_certificates",
            "inactive_on_portal",
            "min_training_percentage",
            "max_training_percentage",
        ]
        read_only_fields = ["user"]


class DashboardMemberSerializer(UserEnrollmentSerializer):
    department_path = serializers.SerializerMethodField(
        help_text="Путь департамента в формате 'Родитель / Текущий'"
    )
    last_login = serializers.SerializerMethodField()
    supervisors = serializers.SerializerMethodField()

    class Meta(UserEnrollmentSerializer.Meta):
        fields = UserEnrollmentSerializer.Meta.fields + (
            "department_path",
            "last_login",
            "supervisors",
        )

    def get_department_path(self, obj):
        aff = getattr(obj, "user_companies", [])
        if aff and len(aff) > 0:
            dept = aff[0].department
            parts = []
            if dept.parent:
                parts.append(dept.parent.name)
            parts.append(dept.name)
            return " / ".join(parts)
        return None

    def get_last_login(self, obj):
        return obj.last_login.strftime("%d %b. %Y г.") if obj.last_login else None

    def get_supervisors(self, obj):
        return ",".join([sup.full_name for sup in obj.supervisors.all()])


class StatisticsCourseProgressSerializer(serializers.Serializer):
    total_count = serializers.IntegerField()
    finished_count = serializers.IntegerField()
    finished_percent = serializers.FloatField()
    in_progress_count = serializers.IntegerField()
    in_progress_percent = serializers.FloatField()
    finished_manually_count = serializers.IntegerField()
    finished_manually_percent = serializers.FloatField()
    not_started_count = serializers.IntegerField()
    not_started_percent = serializers.FloatField()


class StatisticsCourseFinishedSerializer(serializers.Serializer):
    total_count = serializers.IntegerField()
    finished_count = serializers.IntegerField()
    not_finished_count = serializers.IntegerField()


class StatisticsCourseExpiredSerializer(serializers.Serializer):
    not_logged_in = serializers.IntegerField()
    expired_courses = serializers.IntegerField()
    expired_certificates = serializers.IntegerField()