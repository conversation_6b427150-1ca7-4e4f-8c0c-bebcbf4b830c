import logging

from content.models import Settings, Preference, Enrollment
from content.utils.utils import format_human_russian
from lms import settings
from notifications.models import ChannelType
from notifications.services import NotificationService

logger = logging.getLogger("app")


class AppointmentNotificationService:
    """
    Сервис для создания уведомлений о назначениях пользователей на ресурсы.

    Использует настройки(preferences) и связанные с ними параметры
    (payload) для генерации уведомлений.
    """

    def __init__(self, enrollment_ids: list):
        self.enrollment_ids = enrollment_ids
        self.enrollments = []
        self.resources = []
        self.preferences_by_code = {}
        self.settings_map = {}
        self.service = NotificationService()

    def run(self):
        """
        Основной метод запуска сервиса уведомлений.
        Загружает все необходимые данные и создает уведомления.
        """

        logger.info(f"[AppointmentNotificationService] Start for enrollments={self.enrollment_ids}")

        self._load_enrollments()
        self._load_preferences()
        self._load_settings()
        self._notify_users()

    def _load_enrollments(self):
        self.enrollments = Enrollment.objects.filter(id__in=self.enrollment_ids).select_related('resource', 'user', 'due_date_settings')
        self.resources = [e.resource for e in self.enrollments]

    def _load_preferences(self):
        """
        Загружает предпочтения уведомлений на основе типа ресурса.
        Формирует словарь code → preference.
        """
        pref_codes = [f"{r.type}_NOTIFICATION_OF_APPOINTMENT" for r in self.resources]
        preferences = Preference.objects.filter(
            section=Preference.NOTIFICATION,
            code__in=pref_codes
        )
        self.preferences_by_code = {p.code: p for p in preferences}

    def _load_settings(self):
        """
        Загружает настройки для комбинации resource_id и preference_id.
        Формирует карту настроек для быстрого доступа.
        """
        preference_ids = [p.id for p in self.preferences_by_code.values()]
        resource_ids = [r.id for r in self.resources]

        settings_qs = Settings.objects.filter(
            resource_id__in=resource_ids,
            preference_id__in=preference_ids
        )
        self.settings_map = {
            (s.resource_id, s.preference_id): s for s in settings_qs
        }

    def _get_frontend_my_course_url(self):
        return f"{settings.FRONTEND_BASE_URL}/mobile/my-courses"

    def _notify_users(self):
        """
        Создает уведомления пользователям на основе загруженных ресурсов и настроек.
        Использует NotificationService для отправки по EMAIL.
        """
        for enrollment in self.enrollments:
            try:
                # Получаем код предпочтения по типу ресурса
                code = f"{enrollment.resource.type}_NOTIFICATION_OF_APPOINTMENT"
                preference = self.preferences_by_code.get(code)

                if not preference:
                    logger.warning(
                        f"[AppointmentNotificationService] Preference not found for resource={enrollment.resource_id}, code={code}")
                    continue
                # Получаем конкретную настройку
                setting = self.settings_map.get((enrollment.resource_id, preference.id))
                if not setting:
                    logger.warning(
                        f"[AppointmentNotificationService] Settings not found for resource={enrollment.resource_id}, preference_id={preference.id}")
                    continue
                # Извлекаем текст уведомления из payload
                payload = setting.payload or {}
                notification = payload.get('notification', {})
                notify = payload.get('notify', {})

                # Создаем уведомления пачкой
                if notify:
                    subject = notification.get('subject')
                    message = notification.get('message')
                    context = {
                        'title':    enrollment.resource.name,
                        'link':     self._get_frontend_my_course_url(),
                        'due_date': format_human_russian(enrollment.due_date_settings.date)
                    }
                    receiver = enrollment.user
                    self.service.create_notifications_batch(
                        channel=ChannelType.EMAIL,
                        users=[receiver],
                        subject=subject,
                        content=message,
                        context=context,
                        content_object=enrollment
                    )
                    logger.info(
                        f"[AppointmentNotificationService] Notification sent for resource={enrollment.resource_id} to {receiver}")

            except Exception as e:
                logger.error(f"[AppointmentNotificationService] Error processing resource={enrollment.resource_id}: {e}",
                             exc_info=True)
