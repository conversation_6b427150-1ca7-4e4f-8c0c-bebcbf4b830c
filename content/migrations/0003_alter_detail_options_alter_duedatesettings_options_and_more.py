# Generated by Django 5.1.2 on 2025-08-15 10:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='detail',
            options={'ordering': ['id'], 'verbose_name': 'details', 'verbose_name_plural': 'details'},
        ),
        migrations.AlterModelOptions(
            name='duedatesettings',
            options={'ordering': ['id'], 'verbose_name': 'due_date_settings', 'verbose_name_plural': 'due_date_settings'},
        ),
        migrations.AlterModelOptions(
            name='enrollment',
            options={'ordering': ['id'], 'verbose_name': 'enrollment', 'verbose_name_plural': 'enrollments'},
        ),
        migrations.AlterModelOptions(
            name='outline',
            options={'ordering': ['id'], 'verbose_name': 'outline', 'verbose_name_plural': 'outlines'},
        ),
        migrations.AlterModelOptions(
            name='outlineaccesstime',
            options={'ordering': ['id'], 'verbose_name': 'outline_access_time', 'verbose_name_plural': 'outline_access_times'},
        ),
        migrations.AlterModelOptions(
            name='participant',
            options={'ordering': ['id'], 'verbose_name': 'participant', 'verbose_name_plural': 'participants'},
        ),
        migrations.AlterModelOptions(
            name='preference',
            options={'ordering': ['id'], 'verbose_name': 'preference', 'verbose_name_plural': 'preferences'},
        ),
        migrations.AlterModelOptions(
            name='resource',
            options={'ordering': ['id'], 'verbose_name': 'resource', 'verbose_name_plural': 'resources'},
        ),
        migrations.AlterModelOptions(
            name='settings',
            options={'ordering': ['id'], 'verbose_name': 'settings', 'verbose_name_plural': 'settings'},
        ),
        migrations.AlterModelOptions(
            name='tag',
            options={'ordering': ['id'], 'verbose_name': 'tag', 'verbose_name_plural': 'tags'},
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
    ]
