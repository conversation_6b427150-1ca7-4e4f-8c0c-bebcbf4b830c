import logging

from celery import shared_task

from content.services.enrollment.appointment_notifier import AppointmentNotificationService
from content.services.enrollment.auto_enrollment import AutoEnrollmentService
from content.services.enrollment.certificate import CertificateService
from content.services.enrollment.completion_notifier import EnrollmentCompletionNotificationService
from content.services.enrollment.due_date_notification import EnrollmentDueDateNotificationService
from content.services.enrollment.course_reassign import ResourceReassignService
from content.services.enrollment.overdue_enrollments_finish import OverdueEnrollmentsService

logger = logging.getLogger("app")


@shared_task(name="auto_enroll_users")
def auto_enroll_users():
    AutoEnrollmentService().run()


@shared_task(name="send_appointment_notifications")
def send_appointment_notifications(enrollment_ids: list):
    AppointmentNotificationService(enrollment_ids=enrollment_ids).run()


@shared_task
def generate_certificate(enrollment_id: int, settings: dict):
    CertificateService().generate(enrollment_id=enrollment_id, settings=settings)


@shared_task
def check_expire_certificate():
    CertificateService().mark_expired_certificates()


@shared_task(name="send_enrollment_due_date_notification")
def send_enrollment_due_date_notification(enrollment_id):
    EnrollmentDueDateNotificationService(enrollment_id).send_reminder_due_date()


@shared_task(name="send_enrollment_completion_notification")
def send_enrollment_completion_notification(enrollment_id):
    EnrollmentCompletionNotificationService(enrollment_id).send_completion_notifications()


@shared_task(name="resource_reassignment")
def auto_resource_reassignment():
    ResourceReassignService().run()


@shared_task(name="overdue_enrollments_finish")
def overdue_enrollments_finish():
    OverdueEnrollmentsService().run()