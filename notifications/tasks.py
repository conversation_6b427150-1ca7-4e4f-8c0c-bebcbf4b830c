import logging

from django.db import transaction, models
from django.utils.timezone import now

from lms.celery import app
from .factories import SenderFactory
from .models import Notification, NotificationStatus

logger = logging.getLogger('app')


@app.task
def send_pending_notifications():
    # Открываем транзакцию
    with transaction.atomic():
        notifications = (
            Notification.objects.select_for_update()
            .filter(status=NotificationStatus.PENDING)
            .filter(
                models.Q(scheduled_time__isnull=True)
                | models.Q(scheduled_time__lte=now())
            )
        )
        for notification in notifications:
            sender = SenderFactory.get_sender(notification)
            try:
                sender.send()
                notification.status = NotificationStatus.SENT
                notification.sent_time = now()
            except Exception as e:
                # Обработка ошибок
                notification.status = NotificationStatus.FAILED
                notification.error_message = str(e)
            finally:
                # Сохранение изменений в записи
                notification.save()