services:
  api: &api
    image: ${CI_REGISTRY_IMAGE}/${CI_COMMIT_REF_SLUG}:${CI_COMMIT_SHORT_SHA}
    restart: always
    command:
      - /usr/src/app/entrypoint.sh
    volumes:
      - /data/apps/lms/logs:/usr/src/app/logs
      - /data/apps/lms/static:/usr/src/app/static
    environment:
      SECRET_KEY: ${STAGE_SECRET_KEY}
      DEBUG: ${STAGE_DEBUG}
      POSTGRES_DB: ${STAGE_POSTGRES_DB_NAME}
      POSTGRES_USER: ${STAGE_POSTGRES_DB_USER}
      POSTGRES_PASSWORD: ${STAGE_POSTGRES_DB_PASSWORD}
      POSTGRES_HOST: ${STAGE_POSTGRES_HOST}
      POSTGRES_PORT: ${STAGE_POSTGRES_PORT}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_STORAGE_BUCKET_NAME: ${AWS_STORAGE_BUCKET_NAME}
      AWS_S3_ENDPOINT_URL: ${AWS_S3_ENDPOINT_URL}
      AWS_DEFAULT_ACL: ${AWS_DEFAULT_ACL}
      AWS_S3_CUSTOM_DOMAIN: ${STAGE_AWS_S3_CUSTOM_DOMAIN}
      AWS_STATIC_LOCATION: ${STAGE_AWS_STATIC_LOCATION}
      AWS_PUBLIC_MEDIA_LOCATION: ${STAGE_AWS_PUBLIC_MEDIA_LOCATION}
      AWS_PRIVATE_MEDIA_LOCATION: ${STAGE_AWS_PRIVATE_MEDIA_LOCATION}
      SENTRY_DSN: ${STAGE_SENTRY_DSN}
      SONAR_TOKEN: ${STAGE_SONAR_TOKEN}
      REDIS_DEFAULT: ${STAGE_REDIS_DEFAULT}
      REDIS_SESSION: ${STAGE_REDIS_SESSION}
      CELERY_BROKER_URL: ${STAGE_CELERY_BROKER_URL}
      CELERY_RESULT_BACKEND: ${STAGE_CELERY_RESULT_BACKEND}
      LRS_URL: ${STAGE_LRS_URL}
      LRS_VERSION: ${STAGE_LRS_VERSION}
      LRS_USERNAME: ${STAGE_LRS_USERNAME}
      LRS_PASSWORD: ${STAGE_LRS_PASSWORD}
      FRONTEND_BASE_URL: ${STAGE_FRONTEND_BASE_URL}
      KASPI_PEOPLE_MOBILE_API_URL: ${KASPI_PEOPLE_MOBILE_STAGE_API_URL}
      KASPI_PEOPLE_MOBILE_API_KEY: ${KASPI_PEOPLE_MOBILE_STAGE_API_KEY}
    ports:
      - "8010:8000"

  scheduler:
    <<: *api
    command: celery -A lms beat -l info
    user: root
    deploy:
      mode: global
      placement:
        constraints:
          - "node.role==manager"
    ports: [ ]
    depends_on:
      - api

  worker:
    <<: *api
    command: celery -A lms worker -l INFO
    user: root
    deploy:
      mode: global
    ports: [ ]
    depends_on:
      - scheduler

  redis:
    image: repka.kaspi.kz:8443/redis:7.4.2
    container_name: redis
    hostname: redis
    restart: always
    command: redis-server --requirepass "pass"
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
    driver: local
